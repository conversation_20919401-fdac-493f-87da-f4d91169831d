/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-29 14:48:48
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-10 10:56:47
 * @Description:
 */

import { StatusMap, UserType } from '@/constants';
import { RoleType } from './constants/permission/role';
import { judgeIsRixSystemUser } from './utils/permission';

export default function access(initialState: API.InitialStateType) {
  const {
    type = 0,
    cus_status = 0,
    pl_status,
    hm_status,
    tnt_id = 0,
    menu_access = [],
    btn_access = [],
    role_id = 0
  } = initialState?.currentUser || {};
  const allMenuAccess = initialState?.allMenuAccess || [];

  // 所有的权限控制
  const accessObj: any = {};
  allMenuAccess.forEach((v: string) => {
    accessObj[v] = menu_access.includes(v);
  });

  const userType = type || 1;
  const isRixAdmin = userType === UserType.Rix_Admin;
  const isRixSystemUser = judgeIsRixSystemUser(userType);

  const PixalateStatus = pl_status || 2;
  const HumanStatus = hm_status || 2;
  // Rix租户
  const isSampleTraceAuthTnt = [1052, 1047, 1053, 1054, 1075, 1056, 1081, 1067, 1079, 1065, 1077, 1080, 1093].includes(tnt_id);
  const isIVTAuthTnt = [1049].includes(tnt_id);
  const isABTestAuthTnt = [1049, 1046].includes(tnt_id);
  // 1047,1071
  const isBidFloorTnt = [1046, 1047, 1071, 1075].includes(tnt_id);
  const isATCAuth = [1047].includes(tnt_id);
  // 内部用户: 1045(Algorix),  1047(Demo), 1048(Show), 1052(Rix), 1054(RixAD)， 1053(IION), 1046
  const isPMPAuthTnt = [1045, 1047, 1048, 1052, 1054, 1053, 1046, 1075].includes(tnt_id);

  const isStgAuth = [1075, 1047, 1056, 1093].includes(tnt_id);
  const isTransparencyAuthTnt = tnt_id !== 1053;
  const isAppAdsAuthTnt = tnt_id !== 1053;

  const hideAIBoard = [1075].includes(tnt_id);
  const isStreamLAuth = [1075].includes(tnt_id);
  // 单独设置 qps 为 40K
  const isBidedgeAuth = [1079].includes(tnt_id);

  // 上游(demand)的Pixalate/Human配置是否开放
  const isPixalateHuman = [1048, 1049, 1051, 1052, 1056, 1065].includes(tnt_id);

  const isPartner = type === UserType.Partner;
  const SupplyPartner = isPartner && +role_id === RoleType['Supply Partner'];
  const DemandPartner = isPartner && +role_id === RoleType['Demand Partner'];
  const SupplyUser = +type === UserType.Supply; // 上游用户
  const DemandUser = +type === UserType.Demand; // 下游用户

  // 下游用户/下游developer支持
  const isSystemSupplyUser = SupplyUser || SupplyPartner ? +cus_status === StatusMap.Active : !isPartner;
  const isDeveloper = accessObj.DeveloperPermission ? isSystemSupplyUser : false;
  // troubleshooting模块
  const isTroubleUser = isRixAdmin || isSampleTraceAuthTnt;

  // geo policy
  const isGeoPolicyTnt = [1047, 1052, 1093].includes(tnt_id);

  // 可以用来切换账号的特殊逻辑
  const hasSpecialUserId = initialState?.currentUser?.special_user_id;

  if (tnt_id === 1083) {
    return {
      SwitchAccountPermission: true
    };
  }

  return {
    ...accessObj,
    isStreamLAuth,
    isBidedgeAuth,
    hideAIBoard,
    isStgAuth,
    isATCAuth,
    isABTestAuthTnt,
    isPMPAuthTnt,
    isBidFloorTnt,
    isSampleTraceAuthTnt,
    isRixSystemUser,
    isRixAdmin,
    isAppAdsAuthTnt,
    isIVTAuthTnt,
    isPixalateHuman,
    AdvReportPermission: accessObj.AdvReportPermission && (DemandUser || DemandPartner),
    PubReportPermission: accessObj.PubReportPermission && (SupplyUser || SupplyPartner),
    isPixalateAuthTnt: PixalateStatus === StatusMap.Active, // 控制菜单授权 隐藏
    isHumanAuthTnt: HumanStatus === StatusMap.Active, // 控制菜单授权 隐藏
    ReeAIAiBoardPermission: accessObj.ReeAIAiBoardPermission && !hideAIBoard,
    ABTestReportPermission: accessObj.ABTestReportPermission && (isRixAdmin || isABTestAuthTnt),
    DeveloperPermission: isDeveloper,
    SwitchAccountPermission: hasSpecialUserId,
    StrategyFloorPermission: accessObj.StrategyFloorPermission && (isRixAdmin || isBidFloorTnt),
    StrategyIVTPermission: accessObj.StrategyIVTPermission && isIVTAuthTnt,
    StrategyAtcPermission: accessObj.StrategyAtcPermission && isATCAuth,
    StrategyABTestPermission: accessObj.StrategyABTestPermission && (isRixAdmin || isABTestAuthTnt),
    StrategyPMP: accessObj.StrategyPMP && (isRixAdmin || isPMPAuthTnt),
    StrategyGeoPolicyPermission: accessObj.StrategyGeoPolicyPermission && (isRixAdmin || isGeoPolicyTnt),
    PixalateReportPermission: accessObj.PixalateReportPermission && PixalateStatus === StatusMap.Active,
    HumanReportPermission: accessObj.HumanReportPermission && HumanStatus === StatusMap.Active,
    TroubleshootingPermission: accessObj.TroubleshootingPermission && isTroubleUser,
    TroubleshootingSampleTracePermission: accessObj.TroubleshootingSampleTracePermission && isTroubleUser,
    TroubleshootingSampleTraceTaskPermission: accessObj.TroubleshootingSampleTraceTaskPermission && isTroubleUser,
    TroubleshootingSampleTraceTracePermission: accessObj.TroubleshootingSampleTraceTracePermission && isTroubleUser,
    TransparencyPermission: accessObj.TransparencyPermission && isTransparencyAuthTnt,
    TransparencyStgPermission: accessObj.TransparencyStgPermission && isStgAuth,
    // AppAdsCode: accessObj.AppAdsCode && isAppAdsAuthTnt,
    AdsAppInfoCode: accessObj.AdsAppInfoCode && isAppAdsAuthTnt,
    DisabledButton: (value: string | string[]) => {
      // 超管设置不管用 true表示禁用, 传数组表示有一个通过就不禁用，所有都不通过才禁用
      if (Array.isArray(value)) {
        return !value.some(v => btn_access.includes(v));
      } else {
        return !btn_access.includes(value);
      }
    }
  };
}
