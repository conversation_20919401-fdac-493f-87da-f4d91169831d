/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-18 18:10:24
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-06 19:41:12
 * @Description:
 */
/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:27:36
 * @LastEditors: chen<PERSON>dan <EMAIL>
 * @LastEditTime: 2023-08-09 10:38:56
 * @Description:
 */
import React, { useEffect, useRef, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import { StgBreadOptions, StgColumnOptions, StgSearchOption } from '@/constants/transparency/stg-v2';
import RixEngineFont from '@/components/RixEngineFont';
import { useAccess, useModel } from '@umijs/max';
import AddStgChainV2 from '../components/AddStgChainV2';
import OperateRender from '@/components/OperateRender';
import NormalModal from '@/components/Modal/NormalModal';
import { Checkbox, Col, Form, Row } from 'antd';
import { downloadCsv } from '@/utils';
import { ColumnProps } from 'antd/lib/table';
import EditButton from '@/components/Button/EditButton';
const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource, loading, reload } = useModel('useStgChainListV2');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<TransparencyAPI.StgItem>();
  const [columns, setColumns] = useState(StgColumnOptions);
  const [searchOptions, setSearchOptions] = useState(StgSearchOption);
  const [defaultParams] = useState({ status: 1 });
  const [defaultExportColumns] = useState(['publisher_id', 'type', 'developer_website_domain']);
  const [form] = Form.useForm();

  const opColumns: ColumnProps<TransparencyAPI.StgItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      fixed: 'right',
      width: 100,
      render: (txt, params) => (
        <>
          <EditButton onClick={() => handleEdit(params)} disabled={access.DisabledButton('updateSupplyChainAuth')}>
            Edit
          </EditButton>
        </>
      )
    }
  ];

  useEffect(() => {
    reload();
    let columns = StgColumnOptions.map(v => ({ ...v }));
    columns = [...columns, ...opColumns];
    setColumns(columns);
  }, []);

  useEffect(() => {
    const options = StgSearchOption.map(v => ({ ...v }));
    const pIndex = options.findIndex(v => v.key === 'publisher_id');
    const dIndex = options.findIndex(v => v.key === 'developer_website_domain');
    if (pIndex !== -1) {
      options[pIndex].options =
        dataSource?.map((v, index) => {
          return { label: v.publisher_id, value: v.publisher_id, key: index };
        }) || [];
    }
    if (dIndex !== -1) {
      options[dIndex].options =
        dataSource?.map((v, index) => {
          return {
            label: v.developer_website_domain,
            value: v.developer_website_domain,
            key: index
          };
        }) || [];
    }

    setSearchOptions(options);
  }, [dataSource]);

  const handleEdit = (row: TransparencyAPI.StgItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };

  const tableDataRef = useRef<TransparencyAPI.StgItem[]>([]);
  const handleFinish = (values: any) => {
    const { export_columns } = values;
    const fileName = `report_${new Date().getTime()}`;
    const fields: { label: string; value: string }[] = [];
    columns.forEach(item => {
      if (export_columns.includes(item.dataIndex as string)) {
        fields.push({
          label: item.title as string,
          value: item.dataIndex as string
        });
      }
    });
    downloadCsv(fileName, tableDataRef.current, { fields });
    form.resetFields();
    tableDataRef.current = [];
  };
  const handleExport = (tableData: TransparencyAPI.StgItem[]) => {
    const checkBoxOptions = columns
      .map(v => ({
        label: v.title as string,
        value: v.dataIndex as string
      }))
      .filter(v => v.value !== 'operate');

    NormalModal.confirm({
      title: 'Export',
      content: (
        <Form onFinish={handleFinish} form={form}>
          <Form.Item noStyle name="export_columns" initialValue={defaultExportColumns}>
            <Checkbox.Group>
              <Row>
                {checkBoxOptions.map((v, index) => (
                  <Col key={index} span={12}>
                    <Checkbox value={v.value}>{v.label}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      ),
      onOk: () => {
        tableDataRef.current = tableData;
        form.submit();
      },
      onCancel: () => {
        tableDataRef.current = [];
        form.resetFields();
      }
    });
  };
  return (
    <PageContainer options={StgBreadOptions}>
      <FrontTable<TransparencyAPI.StgItem>
        pageTitle="Supply Chain"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Supply Chain',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            icon: <RixEngineFont type="add" />,
            accessCode: 'addSupplyChainAuth'
          }
        ]}
        labelWidth={120}
        scroll={{ y: 'auto' }}
        isFold
        initialValues={defaultParams}
        isExport
        handleExport={handleExport}
      />
      <AddStgChainV2 visible={visible} isEdit={isEdit} item={editItem} onClose={handleClose} onSave={handleSave} />
    </PageContainer>
  );
};

export default Page;
