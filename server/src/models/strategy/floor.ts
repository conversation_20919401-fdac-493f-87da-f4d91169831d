/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-23 17:09:23
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-11 16:11:38
 * @Description:
 */
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { FloorAPI } from '@/types/floor';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class FloorModel implements FloorAPI.FloorModel {
  async isExistedFloor(
    params: FloorAPI.ExistedFloor
  ): Promise<{ count: number }> {
    const { tnt_id, type, buyer_id, seller_id, plm_id, ad_format, country } =
      params;

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['type = ?', type],
      ['buyer_id = ?', buyer_id],
      ['seller_id = ?', seller_id],
      ['plm_id = ?', plm_id],
      ['ad_format = ?', ad_format],
      ['country = ?', country]
    ]);

    const sql = `select count(id) as count from floor ${clause} limit 1`;
    const res = await dbUtil.query(sql, values);
    if (Array.isArray(res) && res.length > 0) {
      return {
        count: res[0].count
      };
    }
    return {
      count: 0
    };
  }

  async getFloorList(
    tnt_id: number,
    cur_time_zone: string,
    plm_id?: number,
    type?: number
  ): Promise<any[]> {
    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['plm_id = ?', plm_id ? plm_id : null],
      ['type = ?', type ? type : null]
    ]);

    const sql = `select
        f.id as id,
        f.type as type,
        f.buyer_id as buyer_id,
        f.seller_id as seller_id,
        f.plm_id as plm_id,
        f.ad_format as ad_format,
        f.country as country,
        f.bid_floor as bid_floor,
        f.op_id as op_id,
        f.status as status,
        ${getTzTimeSql('f.update_time', cur_time_zone)} as update_time,
        u.user_id as user_id,
        u.account_name as account_name,
        u.status as account_status,
        u.type as account_type,
        s.seller_name as seller_name,
        b.buyer_name as buyer_name,
        p.plm_name as plm_name
        from (select id,type,buyer_id,seller_id,plm_id,ad_format,country,bid_floor,op_id,status,update_time,create_time from floor ${clause}) as f
        left join (select user_id,account_name,status,type from user where tnt_id=?) as u on f.op_id=u.user_id
        left join (select seller_id,seller_name from seller where tnt_id=?) as s on f.seller_id=s.seller_id
        left join (select buyer_id,buyer_name from buyer where tnt_id=?) as b on f.buyer_id=b.buyer_id
        left join (select plm_id,plm_name from seller_placement where tnt_id=?) as p on f.plm_id=p.plm_id
        order by f.create_time desc`;
    return await dbUtil.query(sql, [...values, tnt_id, tnt_id, tnt_id, tnt_id]);
  }

  async getAllSupplyPlacement(tnt_id: number) {
    const sql = `select 
        plm.plm_id as plm_id,
        plm.plm_name as plm_name,
        plm.app_id as app_id,
        app.app_name as app_name,
        app.seller_id as seller_id,
        s.seller_name as seller_name
        from (select plm_id,plm_name,app_id from seller_placement where tnt_id=?) as plm
        left join (select app_id,app_name,seller_id from seller_app where tnt_id=?) as app on plm.app_id=app.app_id
        left join (select seller_id,seller_name from seller where tnt_id=?) as s on app.seller_id=s.seller_id`;
    return await dbUtil.query(sql, [tnt_id, tnt_id, tnt_id]);
  }

  async addFloor(params: FloorAPI.AddFloorParams): Promise<boolean> {
    const {
      type,
      buyer_id = [0],
      seller_id = 0,
      plm_id = [0],
      ad_format = 0,
      country = [''],
      bid_floor = 0,
      op_id = 0,
      status = 1,
      tnt_id
    } = params;

    const values: any[][] = [];
    for (const plm of plm_id) {
      for (const c of country) {
        for (const b of buyer_id) {
          values.push([
            type,
            b,
            seller_id,
            plm,
            ad_format,
            c,
            bid_floor,
            op_id,
            status,
            tnt_id
          ]);
        }
      }
    }

    const sql = `insert into floor (type,buyer_id,seller_id,plm_id,ad_format,country,bid_floor,op_id,status,tnt_id) values ? on duplicate key update bid_floor=values(bid_floor),op_id=values(op_id)`;
    return !!(await dbUtil.query(sql, [values]));
  }

  async updateFloor(params: FloorAPI.UpdatFloorParams): Promise<boolean> {
    const { id, tnt_id, bid_floor, op_id, status } = params;

    const floorObj = buildSQLSetClause([
      ['bid_floor', bid_floor],
      ['op_id', op_id],
      ['status', status]
    ]);

    const sql = `update floor set ? where id=? and tnt_id=?`;
    return !!(await dbUtil.query(sql, [floorObj, id, tnt_id]));
  }

  async deleteFloor(params: FloorAPI.DeleteFloorParams): Promise<boolean> {
    const { id, tnt_id } = params;
    const sql = `delete from floor where id=${id} and tnt_id=${tnt_id}`;
    return !!(await dbUtil.query(sql));
  }
}
export default new FloorModel();
