/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-23 17:09:00
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-20 16:36:58
 * @Description:
 */
import { FloorAPI } from '@/types/floor';
import { floorModel } from '@/models';
import { UserType } from '@/constants';

class FloorService implements FloorAPI.FloorService {
  async getFloorList(
    tnt_id: number,
    cur_time_zone: string,
    plm_id?: number,
    type?: number
  ): Promise<any[]> {
    let list = await floorModel.getFloorList(
      tnt_id,
      cur_time_zone,
      plm_id,
      type
    );
    list = list.map((item: any) => {
      [UserType.Rix_Admin, UserType.Rix_Data_Analyst].includes(
        item.account_type
      ) && (item.account_name = 'System');
      item.account_status === 3 && (item.account_name = 'UnKnow');
      delete item.account_type;
      return item;
    });
    return list;
  }
  async getAllSupplyPlacement(tnt_id: number) {
    return await floorModel.getAllSupplyPlacement(tnt_id);
  }
  async addFloor(params: any): Promise<boolean> {
    return await floorModel.addFloor(params);
  }
  async isExistedFloor(
    params: FloorAPI.ExistedFloor
  ): Promise<{ count: number }> {
    return await floorModel.isExistedFloor(params);
  }
  async updateFloor(params: FloorAPI.UpdatFloorParams): Promise<boolean> {
    return await floorModel.updateFloor(params);
  }
  async deleteFloor(params: FloorAPI.UpdatFloorParams): Promise<boolean> {
    return await floorModel.deleteFloor(params);
  }
}

export default new FloorService();
