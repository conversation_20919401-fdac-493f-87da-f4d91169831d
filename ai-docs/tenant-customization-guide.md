# 租户定制化开发指南

## 1. 概述和实现原理

### 1.1 租户定制化概述

SaaS平台通过租户ID（`tnt_id`）和角色ID（`role_id`）实现多租户定制化功能。系统采用硬编码配置的方式，为不同租户提供个性化的功能定制、权限控制和业务逻辑。

### 1.2 实现原理

租户定制化主要通过以下机制实现：

1. **租户ID判断**：通过 `tnt_id` 标识不同租户
2. **角色ID判断**：通过 `role_id` 细化权限控制
3. **硬编码配置**：在前端和后端使用配置文件定义定制化规则
4. **条件分支**：在业务逻辑中使用条件判断实现不同处理

### 1.3 技术架构

```
前端定制化层
├── constants/hardCode/ - 硬编码配置
├── access.ts - 权限访问控制
└── pages/ - 页面级定制化

后端定制化层
├── constants/ - 常量配置
├── services/ - 业务逻辑定制化
└── models/ - 数据模型定制化
```

## 2. 按功能模块分类整理

### 2.1 数据报表定制化

#### 2.1.1 Full Report (全量报表)

**配置文件：** `client/src/constants/hardCode/full-report.tsx`

**定制化租户配置总览：**

| 租户ID | 租户名称 | 定制化内容 | 业务背景 |
|--------|----------|------------|----------|
| 1050 | - | 额外App Name维度，自定义搜索选项和指标 | 需要应用级别的数据分析 |
| 1052 | Rix | 完整的搜索、维度、指标配置 | 内部租户，需要全功能访问 |
| 1053 | IION | 限制的搜索选项和指标，特殊角色配置 | 特定业务需求，数据安全考虑 |
| 1059 | - | 自定义搜索选项、维度和指标 | 已暂停合作 |
| 1071 | - | 自定义配置，支持gross revenue | 已暂停合作 |
| 1075 | - | 增强的搜索选项，支持schain相关维度 | 高级数据分析需求 |
| 1077 | - | 标准配置加CPC指标 | 按点击计费模式 |
| 1093 | - | 标准配置加gross revenue | 收入透明度需求 |

**核心定制化逻辑：**

```typescript
// 自定义整个租户dashboard
export const customDashboard = [1050, 1059, 1057, 1071, 1075, 1052, 1077, 1093];

// 通过租户自定义的角色id来自定义的租户（目前只有1053下面的Data_Custom角色，id为34）
export const customTenant = [1053, 0];
export const customRole: { [key: number]: number[] } = {
  1053: [34]
};
```

**1053租户特殊配置：**
- **租户ID：** 1053 (IION)
- **角色ID：** 34 (Data_Custom)
- **定制内容：** 
  - 限制的搜索选项（不包含placement_id搜索）
  - 自定义维度名称：`placement_id` → `Unit Name (ID)`
  - 限制的指标选项（移除了impression_rate等）
- **文件路径：** 
  - `client/src/constants/hardCode/full-report.tsx:L22-L27`
  - `client/src/constants/hardCode/full-report.tsx:L618-L623`

#### 2.1.2 Billing Report (计费报表)

**配置文件：** `client/src/constants/hardCode/billing-report.tsx`

**定制化配置：**

| 租户ID | 角色ID | 定制内容 | 文件路径 |
|--------|--------|----------|----------|
| 1053 | 34 | 限制搜索选项为['date', 'buyer_id', 'seller_id', 'columns']，维度限制为['day', 'month', 'seller_id', 'buyer_id'] | `client/src/constants/hardCode/billing-report.tsx:L9-L18` |

```typescript
export const customBillingTenant = [1053];
export const customBillingRole: { [key: number]: number[] } = {
  1053: [34]
};
export const customBillingRoleConfig: { [key: number | string]: any } = {
  1053: {
    SearchOption: ['date', 'buyer_id', 'seller_id', 'columns'],
    Dimensions: ['day', 'month', 'seller_id', 'buyer_id']
  }
};
```

#### 2.1.3 数据导出定制化

**后端定制化逻辑：**

**租户1050特殊处理：**
- **定制内容：** 导出数据时增加 `app_name` 字段
- **实现位置：** `server/src/services/data-report/dashboard.ts:L369-L418`

```typescript
// 租户 1050 额外的维度
if (tnt_id === 1050) {
  const app = appList.find(v => v.bundle === item.app_bundle_id);
  item.app_name = app?.app_name || '-';
}
```

**租户1053角色34特殊处理：**
- **定制内容：** 导出数据时将 `placement_id` 替换为 `placement_name(placement_id)` 格式
- **实现位置：** `server/src/services/data-report/dashboard.ts:L369-L418`

```typescript
// 租户 1053 iion 下的 Data_Custom角色 id = 34
if (
  tnt_id === 1053 &&
  [34].includes(role_id || 0) &&
  placementList?.length > 0
) {
  const placement = placementList.find(
    (v: any) => `${v.plm_id}` === `${item.placement_id}`
  );
  if (placement) {
    item.placement_id = `${placement.plm_name}(${item.placement_id})`;
  }
}
```

**CSV导出头部定制化：**
- **配置文件：** `server/src/constants/report/custom-hardcode.ts`
- **定制内容：** 1053租户下Data_Custom角色自定义列名

```typescript
// 1053 iion租户下 Data_Custom角色自定义了Unit Name (ID) -> placement_id 列
export const iionCusTomCsvHeader: { [key: string]: string } = {
  placement_id: 'Unit Name (ID)'
};
```

### 2.2 权限控制定制化

#### 2.2.1 功能权限控制

**配置文件：** `client/src/access.ts`

**特殊租户权限配置：**

| 租户ID列表 | 权限名称 | 权限描述 | 文件位置 |
|------------|----------|----------|----------|
| [1052, 1047, 1053, 1054, 1075, 1056, 1081, 1067, 1079, 1065, 1077, 1080, 1093] | SampleTrace权限 | 样本追踪功能权限 | `client/src/access.ts:L31-L57` |
| [1049, 1056, 1093] | IVT权限 | 无效流量检测权限 | `client/src/access.ts:L31-L57` |
| [1049, 1046] | ABTest权限 | A/B测试功能权限 | `client/src/access.ts:L31-L57` |
| [1046, 1047, 1071, 1075] | BidFloor权限 | 底价策略权限 | `client/src/access.ts:L31-L57` |
| [1047] | ATC权限 | ATC功能权限 | `client/src/access.ts:L31-L57` |
| [1045, 1047, 1048, 1052, 1054, 1053, 1046, 1075] | PMP权限 | 私有市场权限 | `client/src/access.ts:L31-L57` |
| [1075, 1047, 1052, 1056, 1079, 1080, 1093] | STG权限 | STG功能权限 | `client/src/access.ts:L31-L57` |
| 除1053外的所有租户 | Transparency权限 | 透明度功能权限 | `client/src/access.ts:L31-L57` |
| 除1053外的所有租户 | AppAds权限 | App广告功能权限 | `client/src/access.ts:L31-L57` |
| [1075] | 隐藏AI看板 | 隐藏AI功能看板 | `client/src/access.ts:L31-L57` |
| [1075] | StreamL权限 | StreamL功能权限 | `client/src/access.ts:L31-L57` |
| [1079] | Bidedge权限 | Bidedge功能，QPS设置为40K | `client/src/access.ts:L31-L57` |
| [1048, 1049, 1051, 1052, 1056, 1065] | Pixalate/Human权限 | 上游Pixalate/Human配置权限 | `client/src/access.ts:L31-L57` |

#### 2.2.2 Profit Model定制化

**配置文件：** `client/src/utils/permission.ts`

```typescript
export const customProfitModel = {
  demand: [1071, 1052, 1075, 1093],
  supply: [1059, 1052, 1075, 1093]
};
```

**功能说明：** 为特定租户开放demand和supply的profit model功能

### 2.3 用户管理定制化

#### 2.3.1 用户数量限制

**配置位置：** `server/src/controllers/permission/user.ts`

```typescript
// 测试租户用户限制 100
const UserLimitCount =
  tnt_id === 1047 ? DemoTenantUserLimitCount : TenantUserLimitCount;
```

**定制内容：**
- **租户1047（Demo）：** 用户数量限制为100个
- **其他租户：** 默认用户数量限制为20个

#### 2.3.2 特殊用户权限

**租户1083特殊处理：**
- **定制内容：** 激活状态的SA用户设置特殊用户ID
- **实现位置：** `server/src/services/common/user.ts:L119-L168`

```typescript
// 设置特殊用户id
if (tnt_id === 1083 && sa_status === StatusType.Active) {
  user.special_user_id = user_id;
}
```

### 2.4 QPS配置定制化

#### 2.4.1 QPS模式定制化

**特殊QPS配置：**

| 租户ID | QPS配置 | 业务背景 |
|--------|---------|----------|
| 1079 | 40K QPS | Bidedge权限租户，需要更高的QPS配置 |

**实现位置：** `client/src/access.ts:L31-L57`

```typescript
// 单独设置 qps 为 40K
const isBidedgeAuth = [1079].includes(tnt_id);
```

#### 2.4.2 QPS忽略级别

**配置位置：** `server/src/models/strategy/qps.ts`

```typescript
const IgnoreQpsLevel = [
  QpsLevel['supply + ad_format'],
  QpsLevel['supply + bundle,'],
  QpsLevel['supply + country']
];
```

**业务背景：** 某些QPS级别配置不适用于特定租户的业务场景

### 2.5 帮助文档权限定制化

#### 2.5.1 iframe权限处理

**配置位置：** `ai-docs/help-document-access-permissions.md`

**定制化内容：**
- **Guide类别文档：** 需要登录访问
- **Integration/API类别文档：** 无需登录访问
- **iframe环境特殊处理：** 通过cookie认证替代token认证

**权限控制机制：**

```typescript
// Guide 类别文档路径（需要登录访问）
export const GuideDocumentPaths = [
  '/help/get-started',
  '/help/operational-guide',
  '/help/ai-guide',
  '/help/faq'
];

// 无需登录访问的路径
export const IgnoreLoginPath = [
  '/user/login',
  '/help/androidsdk',
  '/help/iossdk',
  '/help/openrtb',
  // ... 其他API文档路径
];
```

## 3. 定制化配置代码示例

### 3.1 前端权限判断示例

```typescript
// 判断是否是定制化租户
const isCustomDashboard = customDashboard.includes(tnt_id);
const isTenantCustomRole = customTenant.includes(tnt_id) && customRole[tnt_id]?.includes(role_id);

// 根据租户获取定制化配置
if (isCustom) {
  const search = customConfig[tnt_id]?.SearchOption;
  const Metrics = customConfig[tnt_id]?.Metrics;
  const Dimensions = customConfig[tnt_id]?.Dimensions;
  // ... 应用定制化配置
}
```

### 3.2 后端数据处理示例

```typescript
// 租户特殊数据处理
if (tnt_id === 1050) {
  const app = appList.find(v => v.bundle === item.app_bundle_id);
  item.app_name = app?.app_name || '-';
}

// 角色特殊处理
if (tnt_id === 1053 && [34].includes(role_id || 0)) {
  const placement = placementList.find(
    (v: any) => `${v.plm_id}` === `${item.placement_id}`
  );
  if (placement) {
    item.placement_id = `${placement.plm_name}(${item.placement_id})`;
  }
}
```

### 3.3 权限控制示例

```typescript
// 功能权限判断
const isSampleTraceAuthTnt = [1052, 1047, 1053, 1054, 1075].includes(tnt_id);
const isIVTAuthTnt = [1049, 1056, 1093].includes(tnt_id);

// 在access对象中应用权限
return {
  ...accessObj,
  StrategyIVTPermission: accessObj.StrategyIVTPermission && isIVTAuthTnt,
  // ... 其他权限
};
```

## 4. 业务背景说明

### 4.1 为什么需要租户定制化

1. **业务差异化需求**：不同租户的业务模式、数据分析需求和功能要求存在差异
2. **数据安全考虑**：某些租户需要限制数据访问范围，保护敏感业务信息
3. **合规要求**：不同租户可能面临不同的合规要求，需要相应的功能限制
4. **合作关系变化**：部分租户可能暂停合作，需要特殊处理
5. **技术能力差异**：不同租户的技术集成能力不同，需要不同的API访问权限

### 4.2 各租户定制化原因

#### 内部租户（1052 Rix、1047 Demo）
- **目的**：提供完整功能访问，用于内部测试和演示
- **特点**：拥有最高权限，可访问所有功能模块

#### 特殊合作伙伴（1053 IION）
- **目的**：根据合作协议限制数据访问范围
- **特点**：特定角色（Data_Custom）只能访问限定的数据维度和指标

#### 技术导向租户（1075、1079）
- **目的**：支持高级技术功能和性能需求
- **特点**：需要更高的QPS配置和特殊的数据维度

#### 历史合作租户（1059、1071）
- **目的**：维护历史配置，但已暂停活跃合作
- **特点**：保留配置但不再维护更新

## 5. 开发和维护指南

### 5.1 添加新的租户定制化

1. **确定定制化类型**：
   - 功能权限定制化：修改 `client/src/access.ts`
   - 报表定制化：修改 `client/src/constants/hardCode/` 下相关文件
   - 数据处理定制化：修改 `server/src/services/` 下相关文件

2. **更新配置文件**：
   - 在相应的配置数组中添加租户ID
   - 定义具体的定制化规则

3. **实现业务逻辑**：
   - 在相关的业务代码中添加条件判断
   - 确保处理逻辑覆盖所有相关场景

### 5.2 维护注意事项

1. **代码可读性**：为每个定制化添加清晰的注释，说明业务背景
2. **向前兼容**：新增定制化不应影响现有租户的功能
3. **测试覆盖**：确保定制化逻辑有相应的测试用例
4. **文档同步**：及时更新技术文档，记录新的定制化配置

### 5.3 定制化配置检查清单

- [ ] 前端权限配置（`access.ts`）
- [ ] 硬编码配置文件（`hardCode/`目录）
- [ ] 后端业务逻辑（`services/`目录）
- [ ] 数据模型处理（`models/`目录）
- [ ] 相关常量定义（`constants/`目录）
- [ ] 文档更新（本文档）

## 6. 技术债务和优化建议

### 6.1 当前问题

1. **硬编码过多**：租户配置分散在多个文件中，维护困难
2. **缺乏统一管理**：没有统一的租户配置管理机制
3. **测试覆盖不足**：定制化逻辑缺乏充分的测试

### 6.2 优化建议

1. **配置中心化**：建立统一的租户配置管理系统
2. **动态配置**：支持通过管理界面动态调整租户配置
3. **配置验证**：建立配置有效性验证机制
4. **监控告警**：对定制化功能使用情况进行监控

### 6.3 重构方向

```typescript
// 建议的统一配置结构
interface TenantConfig {
  tntId: number;
  features: {
    reports: ReportConfig;
    permissions: PermissionConfig;
    qps: QpsConfig;
  };
  customizations: CustomizationConfig;
}
```

## 7. 总结

本文档详细记录了SaaS平台中所有租户定制化的实现逻辑，涵盖了数据报表、权限控制、用户管理、QPS配置等各个方面。通过硬编码配置和条件判断的方式，系统为不同租户提供了个性化的功能体验。

开发者在维护和扩展定制化功能时，应当：
1. 遵循现有的配置模式
2. 保持代码的可读性和可维护性
3. 及时更新相关文档
4. 考虑长期的重构和优化方向

这种定制化方案虽然在短期内能够快速响应业务需求，但长期来看需要向更加灵活和可维护的配置化方向演进。
